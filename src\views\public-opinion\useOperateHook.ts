import * as api from '@/api'
import { AuditType } from '@/api/models'
import { message, Modal } from 'ant-design-vue'

export function useOperateHook(onSearch: () => void) {
  function onAudit(id: GUID, audit: AuditType) {
    let text = ''
    if (audit === AuditType.需要处置) {
      text = '确认提交到舆情审核员审核吗？'
    }
    else if (audit === AuditType.处置) {
      text = '确认上报至教育厅吗？'
    }

    Modal.confirm({
      title: text,
      okText: '确认',
      okType: 'primary',
      cancelText: '取消',
      async onOk() {
        await api.OpinionManage.AuditOpinion_PostAsync({ id, audit })
        onSearch()
        message.success('操作成功')
      },
    })
  }

  async function copyToClipboard(text: string) {
    try {
      await navigator.clipboard.writeText(text)
      message.success('复制成功')
    }
    catch (err: any) {
      message.error(`复制失败: ${err.message}`)
    }
  }

  async function onDel(id: GUID) {
    await api.OpinionManage.Delete_PostAsync({ id })
    message.success('删除成功')
    onSearch()
  }

  async function onCopy(id: GUID) {
    const briefing = await api.OpinionManage.CopySimpleDailyReport_GetAsync({ id })
    copyToClipboard(briefing)
  }

  return { onAudit, onDel, onCopy }
}
