import { HotPublicOpinionAlert } from "./HotPublicOpinionAlert";
import { User } from "./User";
/**热点舆情表*/
export class HotPublicOpinion {
  /**标题*/
  title?: string | null | undefined = null;
  /**舆情摘要*/
  summary?: string | null | undefined = null;
  /**发生时间*/
  occurTime: Dayjs = dayjs();
  /**微博热搜榜*/
  weiboHotSearch?: number | null | undefined = null;
  /**舆情分类*/
  category?: string | null | undefined = null;
  /**正面报道*/
  isPositive: boolean = false;
  hotPublicOpinionAlertId?: GUID = null;
  /**热点舆情预警主表*/
  hotPublicOpinionAlert?: HotPublicOpinionAlert | null | undefined = null;
  createdBy?: GUID = null;
  /**用户*/
  createdUser?: User | null | undefined = null;
  /**添加时间*/
  createdAt?: Dayjs | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
