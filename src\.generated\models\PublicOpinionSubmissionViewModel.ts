import { DepartmentViewModel } from "./DepartmentViewModel";
import { RiskLevel } from "./RiskLevel";
import { Positive } from "./Positive";
import { UserBaseViewModel } from "./UserBaseViewModel";
import { PublicOpinionSubmissionAuditType } from "./PublicOpinionSubmissionAuditType";
/**舆情秘书推送数据记录表*/
export class PublicOpinionSubmissionViewModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**记录相关内容或描述*/
  content?: string | null | undefined = null;
  /**摘要（标题）*/
  summary?: string | null | undefined = null;
  /**舆情链接*/
  url?: string | null | undefined = null;
  /**标签（采用逗号分隔字符串）*/
  tags?: string | null | undefined = null;
  /**事件一级类别*/
  mainCategory?: string | null | undefined = null;
  /**二级类别*/
  category?: string | null | undefined = null;
  /**其他单位名称 涉及部门/单位(输入)*/
  department?: string | null | undefined = null;
  /**关联单位，在接收上报数据时（接口层）自动匹配并赋值 DeptId*/
  deptId?: GUID = null;
  dept?: DepartmentViewModel | null | undefined = null;
  /**发生地*/
  address?: string | null | undefined = null;
  /**来源*/
  source?: string | null | undefined = null;
  /**发布者*/
  publisher?: string | null | undefined = null;
  /**发布时间*/
  published?: Dayjs | null | undefined = null;
  /**风险等级*/
  riskLevel: RiskLevel = 0;
  /**重大事件*/
  serious: boolean = false;
  /**正面报道*/
  positive: Positive = 0;
  /**涉及人数*/
  involved: number = 0;
  /**死亡人数*/
  death: number = 0;
  /**受伤人数*/
  injured: number = 0;
  /**交通事件类别*/
  trafficCategory?: string | null | undefined = null;
  /**所属区县*/
  counties?: string | null | undefined = null;
  /**事发地*/
  address1?: string | null | undefined = null;
  /**事故原因*/
  redundancy1?: string | null | undefined = null;
  /**事故类型*/
  redundancy2?: string | null | undefined = null;
  /**学校或幼儿园名称*/
  redundancy3?: string | null | undefined = null;
  /**是否属于安全隐患*/
  isHiddenDanger?: boolean | null | undefined = null;
  /**页面截图*/
  images?: string[] | null | undefined = [];
  /**上报理由*/
  reason?: string | null | undefined = null;
  /**推送的ip*/
  pushIp?: string | null | undefined = null;
  /**上报时间*/
  createdAt: Dayjs = dayjs();
  /**审核员*/
  auditBy?: GUID = null;
  /**审核员*/
  auditor?: UserBaseViewModel | null | undefined = null;
  /**审核时间*/
  auditAt?: Dayjs | null | undefined = null;
  /**审核状态*/
  auditStatus: PublicOpinionSubmissionAuditType = 0;
}
