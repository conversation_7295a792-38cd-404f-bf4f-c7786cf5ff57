<template>
  <span v-if="getBindValue.readOnly">
    {{ viewValue }}
  </span>
  <div v-else class="flex gap-1">
    <a-tree-select
      v-model:value="internalValue"
      :placeholder="placeholder"
      :tree-line="true"
      :tree-data="treeData"
      tree-node-filter-prop="title"
      show-search
      tree-default-expand-all
      v-bind="$attrs"
      class="flex-1"
      @change="change"
    >
      <template #title="{ title }">
        {{ title }}
      </template>
    </a-tree-select>

    <a-button type="primary" ghost @click="loadData(true)">刷新</a-button>
  </div>
</template>

<script lang='ts' setup>
import type { DepartmentViewModel } from '@/api/models'
import type { TreeProps } from 'ant-design-vue'
import type { FormConfig } from 'ch2-components/lib/form/types'
import { useInjectFormConfig } from 'ch2-components/lib/form/src/useFormConfig'
import { flattenDeep } from 'lodash-es'
import { ref } from 'vue'

defineOptions({
  inheritAttrs: false,
})
const props = defineProps<ApiTreeSelectProps>()

const emit = defineEmits<{
  change: [GUID, DepartmentViewModel]
}>()

interface ApiTreeSelectProps {
  api: () => Promise<any[]>
  placeholder?: string
  readOnly?: boolean
  disabledIds?: GUID[]
}

const { getBindValue } = useInjectFormConfig<ApiTreeSelectProps & FormConfig>({ name: 'select', attrs: useAttrs(), props })

const { getData, setData, setLoadState, getLoadState, loadState } = useCache<DepartmentViewModel[]>('dept-tree')

const internalValue = defineModel('value', { default: Guid.empty })

const treeData = ref<TreeProps['treeData']>([])

const flattenData = ref<Map<GUID, DepartmentViewModel>>(new Map())

const viewValue = computed(() => {
  return flattenData.value.get(internalValue.value)?.name ?? ''
})

/**
 * 将树扁平化
 * @param tree 树结构
 * @returns 扁平化后的节点数组
 */
function flattenTree(tree: DepartmentViewModel[]): DepartmentViewModel[] {
  return flattenDeep(
    tree.map(node => [node, ...(node.children ? flattenTree(node.children) : [])]),
  )
}

const allData = ref<DepartmentViewModel[]>([])

// 加载数据
async function loadData(refresh?: boolean) {
  try {
    if (loadState()) {
      await getLoadState()
    }
    let data: DepartmentViewModel[]
    const originalData = getData()
    if (originalData != null && !refresh) {
      data = originalData
    }
    else {
      setLoadState(true)
      data = await props.api()
      setData(data)
      setLoadState(false)
    }

    allData.value = data

    flattenData.value = new Map(flattenTree(data).map(node => [node.id, node]))
    treeData.value = transformData(data)
  }
  catch (error) {
    setLoadState(false)
    console.error('Failed to load tree data:', error)
  }
}

/**
 * 数据转换函数，将 API 返回的数据转换为树形结构
 * @param data
 */
function transformData(data: DepartmentViewModel[]): TreeProps['treeData'] {
  return data.map(item => ({
    title: item.name!,
    value: item.id!,
    key: item.id!,
    disabled: props.disabledIds?.includes(item.id!),
    children: item.children ? transformData(item.children) : undefined,
  }))
}

function change(v: GUID) {
  const data = flattenData.value.get(v)
  emit('change', v, data!)
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>

</style>
