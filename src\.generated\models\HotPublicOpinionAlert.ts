import { UploadFileInfo } from "./UploadFileInfo";
import { HotPublicOpinion } from "./HotPublicOpinion";
import { User } from "./User";
/**热点舆情预警主表*/
export class HotPublicOpinionAlert {
  /**标题*/
  title?: string | null | undefined = null;
  /**时间*/
  alertTime: Dayjs = dayjs();
  /**附件*/
  attachmentId?: GUID = null;
  /**文件信息*/
  attachment?: UploadFileInfo | null | undefined = null;
  /**描述*/
  description?: string | null | undefined = null;
  /**关联热点舆情*/
  hotPublicOpinions?: HotPublicOpinion[] | null | undefined = [];
  createdBy?: GUID = null;
  /**用户*/
  createdUser?: User | null | undefined = null;
  /**添加时间*/
  createdAt?: Dayjs | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
