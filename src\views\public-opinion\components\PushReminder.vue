<template>
  <a-modal v-model:open="open" title="合作单位推送提醒" :width="tableData.length <= 0 ? '800px' : '80%'" destroy-on-close mask-closable>
    <div class="flex">
      <div class="mr-4 border-r border-border border-r-solid pr-4 space-y-4" :class="tableData.length <= 0 ? 'w-800px' : 'w-520px'">
        <div class="space-y-4">
          <div>
            <span class="c-text-base">舆情描述：</span>
            <span class="c-text">{{ currentData.summary }}</span>
          </div>
          <div>
            <span class="c-text">发帖时间：</span>
            <span class="c-text">{{ dateTime(currentData.published, 'YYYY-MM-DD HH:mm') }}</span>
          </div>
          <div>
            <span class="c-text-base">发帖渠道：</span>
            <span class="c-text">{{ currentData.source }}</span>
          </div>
          <div>
            <span class="c-text-base">发帖人：</span>
            <span class="c-text">{{ currentData.publisher }}（ {{ currentData.source }}号： {{ currentData.sourceId || '--' }} ）</span>
          </div>
          <div>
            <span class="c-text-base">原文链接：</span>
            <span class="c-text">{{ currentData.url }}</span>
          </div>
          <div>
            <span class="c-text-base">截图：</span>
            <span v-for="(img, idx) in currentData.images" :key="idx" class="coverBox size-140px overflow-hidden">
              <c-image
                :src="joinFilePathById(img)" alt="avatar" :preview="true"
                style="height: 80px; width:80px ; object-fit:cover"
              />
            </span>
          </div>
        </div>
        <a-divider dashed />
        <div class="">
          <div class="flex items-center">
            <span class="c-text-base">推送单位：</span>
            <C2TreeSelect
              v-model:value="currentDeptId"
              :read-only="!isEdit"
              :api="() => api.DepartmentManage.GetAllDepartmentsAsync({ isCooperate: true })"
              class="w-280px"
              @change="deptChange"
            />
            <c-icon-form-outlined v-show="!isEdit" class="ml-2 cursor-pointer text-4 text-primary" @click="isEdit = true" />
          </div>
          <div v-for="(item, index) in userData" :key="index" space-y-2>
            <div>
              <span class="c-text-base">推送对象{{ index + 1 }}：</span>
              <span class="c-text">{{ item.name }}</span>
            </div>
            <div>
              <span class="c-text-base">推送渠道：</span>
              <span class="c-text">
                <a-checkbox-group v-model:value="channelMap[item.id]" :options="channelOptions" />
              </span>
            </div>
          </div>
          <a-divider />
          <div>
            <a-checkbox v-model:checked="sendForm.force"><span class="text-base">追加发送</span></a-checkbox>
            <span class="c-error">存在推送记录请选择追加推送，推送记录不存在则默认。</span>
          </div>
        </div>
      </div>
      <div v-if="tableData.length > 0" class="flex-1">
        <a-card title="推送记录" style="width: 100%">
          <c-table :columns="columns" :data-source="tableData" size="small" :pagination="false" :scroll="{ y: 520 }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'deptPushLogs'">
                <!-- <div v-html="formatDeptPushLogs(record.deptPushLogs)" /> -->
                <div v-for="(item, index) in record.deptPushLogs" :key="item.id">
                  <span>({{ index + 1 }})</span>
                  <span>{{ item.text }}；</span>
                  <span>【<b>{{ item.pushUser?.name }}</b> —— {{ item.channels?.map((v: DeptPushChannelLog) => (`${DeptPushChannel[v.channel]}-${PushStatus[v.status]}`)).join('、') }}】</span>
                  <span class="mx-2" :class="item.status === PushStatus.失败 ? 'c-error' : (item.status === PushStatus.成功 ? 'c-success' : '')">
                    {{ PushStatus[item.status] }}
                  </span>
                </div>
              </template>
              <template v-if="column.key === 'recentlyPushTime'">
                <div>{{ dateTime(record.recentlyPushTime) }}</div>
              </template>
            </template>
          </c-table>
        </a-card>
      </div>
    </div>
    <template #footer>
      <a-button key="back" @click="open = false">稍后推送</a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="onSend">立即推送</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { DeptMainPush, DeptPushChannelLog, PublicOpinion, PublicOpinionViewModel, PushTarget, UserViewModel } from '@/api/models'
import * as api from '@/api'
import { DeptPushChannel, DeptPushType, PushStatus } from '@/api/models'
import { message } from 'ant-design-vue'

const props = defineProps<{
  currentData: PublicOpinion | PublicOpinionViewModel
}>()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const loading = ref(false)

const sendForm = ref({
  abstractText: '',
  pushUser: Guid.empty,
  publicOpinionId: props.currentData.id,
  force: false,
})

const channelMap = ref<Record<string, DeptPushChannel[]>>({})

const channelOptions = ref([
  { label: '微信', value: DeptPushChannel.微信 },
  { label: '电话', value: DeptPushChannel.电话 },
  { label: '邮箱', value: DeptPushChannel.邮箱 },
])

const currentDeptId = ref<GUID>(Guid.empty)

const isEdit = ref(false)

const userData = ref<UserViewModel[]>([])

async function getUserData() {
  userData.value = await api.DeptPushLogs.GetPushUserAsync({ departmentId: currentDeptId.value })
}

function deptChange() {
  getUserData()
}

function getSubmitData(): PushTarget[] {
  return userData.value.map(user => ({
    pushUserId: user.id as string,
    channels: channelMap.value[user.id!] || [],
  }))
}

async function onSend() {
  loading.value = true
  const submitData = getSubmitData()

  try {
    await api.OpinionManage.SendPublicOpinionsToCooperateDept_PostAsync({ abstractText: sendForm.value.abstractText, publicOpinionId: props.currentData.id, force: sendForm.value.force }, submitData)
    message.success('推送成功')
    loading.value = false
    open.value = false
  }
  catch (error: any) {
    console.log(error)
    loading.value = false
  }
}

const tableData = ref<DeptMainPush[]>([])

const columns = ref([
  {
    title: '推送信息',
    dataIndex: 'deptPushLogs',
    key: 'deptPushLogs',
  },
  {
    title: '推送时间',
    dataIndex: 'recentlyPushTime',
    key: 'recentlyPushTime',
    width: 200,
  },
])

async function getUserPushStatus() {
  tableData.value = await api.DeptPushLogs.GetUserPushStatusAsync({ departmentId: currentDeptId.value, entityId: props.currentData.id, entityType: DeptPushType.疑似舆情 })
}

watch(
  () => open.value,
  (newVal) => {
    if (newVal && props.currentData?.deptId) {
      currentDeptId.value = props.currentData.deptId
      getUserData()
      getUserPushStatus()
    }
  },
)
</script>
