import { DepartmentViewModel } from "./DepartmentViewModel";
export class PublicOpinionTopicViewModel {
  id?: GUID = null;
  /**标题*/
  title?: string | null | undefined = null;
  /**摘要*/
  summary?: string | null | undefined = null;
  /**图片*/
  img?: string | null | undefined = null;
  /**标签*/
  tag?: string | null | undefined = null;
  /**标签背景色*/
  tagColor?: string | null | undefined = null;
  /**开始*/
  start: Dayjs = dayjs();
  /**结束*/
  end?: Dayjs | null | undefined = null;
  /**关联的单位*/
  deptIds?: Array<GUID> = [];
  departments?: DepartmentViewModel[] | null | undefined = [];
}
