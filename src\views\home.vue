<template>
  <div class="bg-bg-container p-16px c-text">
    <div
      class="grid grid-cols-5 w-100% gap-4 max-3xl:grid-cols-4 max-4xl:grid-cols-5 max-lg:grid-cols-2 max-sm:grid-cols-1 max-xl:grid-cols-3"
    >
      <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-2">
          <div class="i-fluent-text-bullet-list-square-edit-20-regular text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">今日新增舆情</span>
            <span><b>{{ countData.today?.value }}</b>条</span>
          </div>
          <div class="text-right text-12px">
            <span>上报教育厅</span>
            <span class="ml-24px text-#e33c64">{{ countData.today?.child?.[0]?.value }}</span>
          </div>
        </div>
      </div>

      <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-2">
          <div class="i-material-symbols-contract-edit-outline-sharp text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">本周新增舆情</span>
            <span><b>{{ countData.week?.value }}</b>条</span>
          </div>
          <div class="text-right text-12px">
            <span>上报教育厅</span>
            <span class="ml-24px text-#e33c64">{{ countData.week?.child?.[0]?.value }}</span>
          </div>
        </div>
      </div>

      <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-2">
          <div class="i-material-symbols-light-list-alt-add-outline-rounded text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">合作单位推送</span>
            <span><b>{{ countData.deptPush?.value }}</b>条</span>
          </div>
          <div class="flex justify-end text-right text-12px">
            <div v-for="item, index in countData.deptPush?.child" :key="index" class="flex text-nowrap last:ml-2">
              <span class="text-#666666">{{ item.label }}</span>
              <span
                class="ml-4px flex"
                :class="item.value > 0 ? 'c-#0AB389' : item.value < 0 ? 'c-#FF3D3D' : 'c-#666666'"
              >
                <div v-if="item.value > 0" class="i-material-symbols-light-arrow-upward-alt-rounded" />
                <div v-else-if="item.value < 0" class="i-material-symbols-light-arrow-downward-alt-rounded" />
                {{ item.value.toFixed(2) }} %
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-2">
          <div class="i-fluent-calendar-todo-32-light text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">本周热点舆情</span>
            <span><b>{{ countData.hotopinion?.value }}</b>条</span>
          </div>
          <div class="flex justify-end text-right text-12px">
            <div v-for="item, index in countData.hotopinion?.child" :key="index" class="flex text-nowrap last:ml-2">
              <span class="text-#666666">{{ item.label }}</span>
              <span
                class="ml-4px flex"
                :class="item.value > 0 ? 'c-#0AB389' : item.value < 0 ? 'c-#FF3D3D' : 'c-#666666'"
              >
                <div v-if="item.value > 0" class="i-material-symbols-light-arrow-upward-alt-rounded" />
                <div v-else-if="item.value < 0" class="i-material-symbols-light-arrow-downward-alt-rounded" />
                {{ item.value.toFixed(2) }} %
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-16px">
          <div class="i-material-symbols-percent-rounded text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">校安舆情预警</span>
            <span><b>{{ (Number(countData.endEventRatio?.value) * 100).toFixed(2) }}%</b></span>
          </div>
          <div class="flex justify-end text-right text-12px">
            <div v-for="item, index in countData.endEventRatio?.child" :key="index" class="flex text-nowrap last:ml-2">
              <span class="text-#666666">{{ item.label }}</span>
              <span
                class="ml-4px flex"
                :class="item.value > 0 ? 'c-#0AB389' : item.value < 0 ? 'c-#FF3D3D' : 'c-#666666'"
              >
                <div v-if="item.value > 0" class="i-material-symbols-light-arrow-upward-alt-rounded" />
                <div v-else-if="item.value < 0" class="i-material-symbols-light-arrow-downward-alt-rounded" />
                {{ item.value.toFixed(2) }} %
              </span>
            </div>
          </div>
        </div>
      </div> -->

      <div class="h-80px min-w-270px w-100% flex justify-between rounded-8px bg-fill-tertiary px-16px py-16px">
        <div class="rounded-30px bg-bg-container p-2">
          <div class="i-fluent-text-bullet-list-square-clock-20-regular text-28px c-#0AB389" />
        </div>
        <div class="ml-16px flex flex-1 flex-col justify-between">
          <div class="flex justify-between">
            <span class="font-bold">舆情总数</span>
            <span><b>{{ countData.publicOpinionCount?.value }}</b>条</span>
          </div>
          <div class="flex justify-end text-right text-12px">
            <div
              v-for="item, index in countData.publicOpinionCount?.child" :key="index"
              class="flex text-nowrap last:ml-2"
            >
              <span class="text-#666666">{{ item.label }}</span>
              <span
                class="ml-4px flex"
                :class="item.value > 0 ? 'c-#0AB389' : item.value < 0 ? 'c-#FF3D3D' : 'c-#666666'"
              >
                <div v-if="item.value > 0" class="i-material-symbols-light-arrow-upward-alt-rounded" />
                <div v-else-if="item.value < 0" class="i-material-symbols-light-arrow-downward-alt-rounded" />
                {{ item.value.toFixed(2) }} %
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="my-4 flex flex-wrap gap-4 c-text">
    <div
      class="group h-48px min-w-150px flex cursor-pointer items-center justify-center bg-bg-container px2 text-center shadow-[0_2px_3px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out hover:(bg-[linear-gradient(90deg,rgba(9,179,136,1)_0%,rgba(80,173,250,1)_100%)] c-#fff)"
      @click="onAdd"
    >
      <div class="i-solar-list-arrow-up-broken text-6 c-#4876FF group-hover:c-#fff" />
      <div class="ml-16px text-18px">舆情录入</div>
    </div>
    <div
      class="group h-48px min-w-220px flex cursor-pointer items-center justify-center bg-bg-container px2 text-center shadow-[0_2px_3px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out hover:(bg-[linear-gradient(90deg,rgba(9,179,136,1)_0%,rgba(80,173,250,1)_100%)] c-#fff)"
      @click="router.push('/to-do-list')"
    >
      <div class="i-formkit-people text-6 c-#4BADF2 group-hover:c-#fff" />
      <div class="ml-16px text-18px">国内热点舆情录入</div>
    </div>
    <div
      class="group h-48px min-w-240px flex cursor-pointer items-center justify-center bg-bg-container px2 text-center shadow-[0_2px_3px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out hover:(bg-[linear-gradient(90deg,rgba(9,179,136,1)_0%,rgba(80,173,250,1)_100%)] c-#fff)"
      @click="exportFile(ToDayBriefing.涉教师)"
    >
      <div class="i-formkit-linkexternal text-6 c-#4BADF2 group-hover:c-#fff" />
      <div class="ml-16px text-18px">今日涉教师舆情导出</div>
    </div>
    <div
      class="group h-48px min-w-240px flex cursor-pointer items-center justify-center bg-bg-container px2 text-center shadow-[0_2px_3px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out hover:(bg-[linear-gradient(90deg,rgba(9,179,136,1)_0%,rgba(80,173,250,1)_100%)] c-#fff)"
      @click="exportFile(ToDayBriefing.涉高校)"
    >
      <!-- <c-icon-export-outlined /> -->
      <div class="i-formkit-linkexternal text-6 c-#4BADF2 group-hover:c-#fff" />
      <div class="ml-16px text-18px">今日涉高校舆情导出</div>
    </div>
    <div
      class="group h-48px min-w-230px flex cursor-pointer items-center justify-center bg-bg-container px2 text-center shadow-[0_2px_3px_rgba(0,0,0,0.25)] transition-all duration-300 ease-in-out hover:(bg-[linear-gradient(90deg,rgba(9,179,136,1)_0%,rgba(80,173,250,1)_100%)] c-#fff)"
      @click="router.push('/to-do-list')"
    >
      <div class="i-formkit-filedoc text-6 c-#4BADF2 group-hover:c-#fff" />
      <div class="ml-16px text-18px">合作单位日报导出</div>
    </div>
  </div>

  <div class="mt-4 bg-bg-container py-4 c-text">
    <div class="flex items-center c-text">
      <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
      <div class="ml-4 text-16px font-bold">今日舆情（{{ totals }}）</div>
    </div>
    <div class="px-4">
      <c-pro-table
        ref="proTableRef"
        :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')"
        :row-key="(record) => record.id"
        size="small"
        :columns="columns"
        :api="api.OpinionManage.GetListAsync"
        immediate
        operation
        :show-search="false"
        :show-tool-btn="false"
        :get-params="params"
        @after-fetch="afterFetch"
      >
        <template #header>
          <a-input-search
            v-model:value="params.summary"
            placeholder="请输入关键词"
            style="width: 280px"
            size="large"
            @search="proTableRef.search()"
          />
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'summary'">
            <div v-if="record.dept?.isCooperate" class="w-42px rounded-md bg-#F0B800 text-center text-3 c-#fff">合作</div>
            <a @click="onEdit(record, true)">{{ record.summary }}</a>
          </template>
          <template v-if="column.dataIndex === 'content'">
            <div>{{ record.content }}</div>
            <div class="flex space-x-2">
              <div v-for="(img, idx) in record.images" :key="idx" class="coverBox size-30px overflow-hidden">
                <c-image
                  :src="joinFilePathById(img)" alt="avatar" :preview="true"
                  style="height: 30px; width:30px ; object-fit:cover"
                />
              </div>
            </div>
            <div>
              <a v-for="(url, idx) in record.url?.split(/\r?\n/)" :key="idx" :href="url" target="_blank"> {{ url }} </a>
            </div>
          </template>
          <template v-if="column.dataIndex === 'publisher'">
            <div class="flex items-center">
              <c-image
                :src="joinFilePathById(getByName(record.source)?.iconId!)" alt="avatar" :preview="true"
                style="height: 20px; width:20px ; object-fit:cover"
              />
              <span class="ml-1">{{ record.publisher }}</span>
            </div>
            <div>id: {{ record.sourceId }}</div>
            <div class="c-text-secondary">{{ dateTime(record.published, 'YYYY-MM-DD HH:mm') }}</div>
          </template>
          <template v-if="column.dataIndex === 'createdUser'">
            <div>{{ record.createdUser?.userName }}</div>
            <span class="c-text-secondary">{{ dateTime(record.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
          </template>
          <template v-if="column.dataIndex === 'dept'">
            <div class="text-center">
              <div>{{ record.dept?.name }}</div>
              <span>{{ record.address }}</span>
              <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导]) && record.dept?.isCooperate"> <a @click="onReminder(record)"><c-icon-comment-outlined class="mr-1" />提醒</a></div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'auditStatus'">
            <div class="text-center">
              <a-tag class="mr-0!" :color="auditTypeColor(record.auditStatus)">
                {{ opinionTypeText(record.auditStatus) }}
              </a-tag>
              <div class="mt-1">
                <a v-if="$auth(_Role.舆情监测中心领导) && record.auditStatus === AuditType.需要处置" @click="onAudit(record.id, AuditType.处置)">上报</a>
                <a v-if="$auth(_Role.舆情监测人员) && record.auditStatus === AuditType.未读" @click="onAudit(record.id, AuditType.需要处置)">上报</a>
              </div>
            </div>
          </template>
        </template>
        <template #operation="{ record }">
          <div v-if="$auth([_Role.舆情监测人员, _Role.舆情监测中心领导])">
            <a @click="onEdit(record, false)">编辑</a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确认删除此舆情吗?"
              ok-text="确认"
              cancel-text="取消"
              @confirm="onDel(record.id)"
            >
              <span class="cursor-pointer c-error">删除</span>
            </a-popconfirm>
          </div>
          <div>
            <a @click="onCopy(record.id)">简报复制</a>
          </div>
        </template>
      </c-pro-table>
    </div>
  </div>

  <!-- <div class="mt-4 bg-bg-container py-4">
    <div class="flex items-center c-text">
      <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
      <div class="ml-4 text-16px font-bold">校园安全疑似舆情（7）</div>
    </div>
    <div class="mt-4 px-4">

    </div>
  </div> -->

  <EditForm v-model:open="open" v-model="currentObj" :read-only="readOnly" @save="proTableRef.search()" />

  <PushReminder v-model:open="pushReminderOpen" :current-data="currentObj" />
</template>

<script lang="ts" setup>
import type { PublicOpinionViewModel } from '@/.generated/models'
import { AuditType, PublicOpinionEditModel, StatisticsViewModel, ToDayBriefing } from '@/.generated/models'
import * as api from '@/api'
import { useSocialMediaCache } from '@/hooks/useSocialMediaCache'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import EditForm from '@/views/public-opinion/components/EditForm.vue'
import PushReminder from '@/views/public-opinion/components/PushReminder.vue'
import { useOperateHook } from '@/views/public-opinion/useOperateHook'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const { getByName } = useSocialMediaCache()

definePage({
  meta: {
    title: '首页',
    layout: 'admin',
    local: true,
    icon: 'HomeOutlined',
    layoutRoute: {
      meta: {
        title: '首页',
        icon: 'HomeOutlined',
        order: 9999,
      },
    },
  },
})

const { open, readOnly, currentObj, onAdd, onEdit } = useEditHook()

const { columns, params, proTableRef, afterFetch, totals } = useTableHook()

function onSearch() {
  proTableRef.value?.search()
}

const { onAudit, onDel, onCopy } = useOperateHook(onSearch)

const { pushReminderOpen, onReminder } = useReminderHook()

const router = useRouter()

const countData = ref(new StatisticsViewModel())

async function getData() {
  try {
    countData.value = await api.Statistics.GetHomeAsync()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function exportFile(type: ToDayBriefing) {
  useDownload(() => api.OpinionManage.ExportSimpleDailyReport_GetAsync({ toDayBriefing: type }))
}

function useEditHook() {
  const open = ref(false)

  const readOnly = ref(false)

  const currentObj = ref(new PublicOpinionEditModel())

  function onAdd() {
    readOnly.value = false
    currentObj.value = new PublicOpinionEditModel()
    open.value = true
  }

  function onEdit(record: PublicOpinionViewModel, isRead: boolean) {
    currentObj.value = viewModelToEditModel(record, new PublicOpinionEditModel())
    readOnly.value = isRead
    open.value = true
  }

  return { open, readOnly, currentObj, onAdd, onEdit }
}

function useTableHook() {
  const proTableRef = useTemplateRef('proTableRef')

  const totals = ref(0)

  const params = {
    summary: '',
    createdStart: dayjs().subtract(1, 'day').hour(12).minute(0).second(0).millisecond(0),
    createdEnd: dayjs(),
  }

  const columns = ref([
    {
      title: '舆情摘要',
      dataIndex: 'summary',
    },
    { title: '发帖人信息', dataIndex: 'publisher', width: 240 },
    { title: '原文内容', dataIndex: 'content' },
    { title: '舆情类别', dataIndex: 'category', width: 120 },
    { title: '涉及地区/单位', dataIndex: 'dept', width: 150 },
    { title: '录入人', dataIndex: 'createdUser', width: 160 },
    { title: '上报状态', dataIndex: 'auditStatus', width: 120, align: 'center' },
  ])

  const tableData = ref([
    {
      summary: '测试1',
    },
    {
      summary: '测试2',
    },
    {
      summary: '测试4',
    },
  ])

  function afterFetch(e: { totals: number }) {
    totals.value = e.totals
  }

  return { columns, tableData, totals, params, afterFetch, proTableRef }
}

function useReminderHook() {
  const pushReminderOpen = ref(false)

  function onReminder(record: PublicOpinionViewModel) {
    currentObj.value = record

    pushReminderOpen.value = true
  }

  return { pushReminderOpen, onReminder }
}

onMounted(() => {
  getData()
})
</script>

<style scoped lang="less">
.home-nav {
  cursor: pointer;

  &:hover {
    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.25);
  }
}

.ch2-table-striped {
  background: rgba(9, 179, 136, 0.05);
}

:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
</style>
