import { RiskLevel } from "./RiskLevel";
import { PublicOpinionSubmissionAuditType } from "./PublicOpinionSubmissionAuditType";
/**舆情提交列表查询请求参数*/
export class SubmissionQueryRequest {
  /**关键词（匹配摘要或内容）*/
  keyword?: string | null | undefined = null;
  /**是否协同部门*/
  cooperateDept?: boolean | null | undefined = null;
  /**风险等级（多选）*/
  riskLevel?: RiskLevel[] | null | undefined = [];
  /**一级分类（多选）*/
  mainCategory?: string[] | null | undefined = [];
  /**发布时间范围（起始）*/
  startTime?: Dayjs | null | undefined = null;
  /**发布时间范围（结束）*/
  endTime?: Dayjs | null | undefined = null;
  /**查询审核历史*/
  audited: boolean = false;
  auditStatus?: PublicOpinionSubmissionAuditType | null | undefined = null;
}
