<template>
  <div class="mb-4 flex space-x-4">
    <div class="h-24 flex flex-1 flex-col justify-between gap-4 border border-l-4 border-l-primary rounded-xl border-l-solid bg-white px-4 py-3 shadow-sm">
      <div class="text-base">合作单位</div>
      <div class="text-right text-6 c-text font-bold">{{ counts.deptCount }}</div>
    </div>
    <div class="h-24 flex flex-1 flex-col justify-between gap-4 border border-l-4 border-l-success rounded-xl border-l-solid bg-white px-4 py-3 shadow-sm">
      <div class="text-base">今日待推送日报</div>
      <div class="text-right text-6 c-text font-bold">{{ counts.waitPushToDayReport }}</div>
    </div>
    <div class="h-24 flex flex-1 flex-col justify-between gap-4 border border-l-4 border-l-info rounded-xl border-l-solid bg-white px-4 py-3 shadow-sm">
      <div class="text-base">今日已推送日报</div>
      <div class="text-right text-6 c-text font-bold">{{ counts.pushToDayReport }}</div>
    </div>
  </div>
  <a-card title="合作单位" :bordered="false" style="width: 100%">
    <template #extra><a>刷新</a></template>
    <c-pro-table
      ref="proTableRef" size="small" :row-key="(record) => record.deptId"
      :columns="columns"
      :api="api.DeptPushLogs.GetDeptAsync"
      immediate serial-number
      operation
    >
      <template #header>
        <a-input-search
          v-model:value="deptName"
          placeholder="请输入单位名称"
          style="width: 280px"
          size="large"
          @search="proTableRef?.search()"
        />
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'deptName'">
          <a @click="router.push({ path: '/push-records/detail', query: { id: record.deptId } })">{{ record.deptName }}</a>
        </template>
        <template v-if="column.dataIndex === 'isPushToDayReport'">
          <a-tag :color="record.isPushToDayReport === PushStatus.成功 ? 'success' : record.isPushToDayReport === PushStatus.失败 ? 'error' : 'default'">{{ PushStatus[record.isPushToDayReport] }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'contractStartTime'">
          {{ dateTime(record.contractStartTime, 'YYYY-MM-DD') }} ~ {{ dateTime(record.contractEndTime, 'YYYY-MM-DD') }}
        </template>
        <template v-if="column.dataIndex === 'autoPush'">
          <a-tag :color="record.autoPush ? 'success' : 'default'">{{ record.autoPush ? '已开启' : '已关闭' }}</a-tag>
        </template>
      </template>

      <template #operation="{ record }">
        <a @click="addRecord(record)">新增推送</a>
      </template>
    </c-pro-table>
  </a-card>
</template>

<script lang='ts' setup>
import type { DeptMainPushView } from '@/api/models'
import * as api from '@/api'
import { DeptPushStatistics, PushStatus } from '@/api/models'
import { useRouter } from 'vue-router'

definePage({
  meta: {
    title: '合作高校推送记录',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '推送管理及记录',
        local: true,
        icon: 'UnorderedListOutlined',
        order: 7,
      },
    },
  },
})

const router = useRouter()

const proTableRef = useTemplateRef('proTableRef')

const deptName = ref('')

const columns = ref([
  {

    title: '单位名称',
    dataIndex: 'deptName',
  },
  { title: '今日推送舆情', dataIndex: 'toDayPubliceOpinionCount', align: 'center' },
  { title: '七日内推送舆情', dataIndex: 'sevenDaysCount', align: 'center' },
  { title: '今日舆情日报', dataIndex: 'isPushToDayReport', align: 'center' },
  { title: '舆情上报要求', dataIndex: 'remark' },
  { title: '合同有效期', dataIndex: 'contractStartTime', align: 'center' },
  { title: '自动推送', dataIndex: 'autoPush', align: 'center' },
])

function addRecord(record: DeptMainPushView) {
  console.log('record', record)
}

const counts = ref(new DeptPushStatistics())

async function getCounts() {
  counts.value = await api.DeptPushLogs.GetPushStatisticsAsync()
}

onMounted(() => {
  getCounts()
})
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}

:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}
</style>
