import { LoginResultLog } from "./LoginResultLog";
export class IdentityUserLoginLog<TKey> {
  id?: TKey | null | undefined = null;
  userId?: TKey | null | undefined = null;
  userName?: string | null | undefined = null;
  loginTime: Dayjs = dayjs();
  userAgent?: string | null | undefined = null;
  ipAddress4?: string | null | undefined = null;
  ipAddress6?: string | null | undefined = null;
  result: LoginResultLog = 0;
  provider?: string | null | undefined = null;
}
