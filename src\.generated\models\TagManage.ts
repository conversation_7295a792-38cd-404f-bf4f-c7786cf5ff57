import { TagType } from "./TagType";
export class TagManage {
  /**标签值*/
  value?: string | null | undefined = null;
  /**标签描述*/
  description?: string | null | undefined = null;
  /**上级标签*/
  parentId?: GUID = null;
  parent?: TagManage | null | undefined = null;
  /**标签类型*/
  tagType?: TagType | null | undefined = null;
  /**是否显示涉及人数,以二级分类为主*/
  isShowNumberPeopleInvolved?: boolean | null | undefined = null;
  /**是否显示受伤人数,以二级分类为主*/
  isShowTheInjured?: boolean | null | undefined = null;
  /**是否显示死亡人数,以二级分类为主*/
  isShowNumberOfDeaths?: boolean | null | undefined = null;
  /**是否显示安全隐患*/
  isShowIsHiddenDanger?: boolean | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
