<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <div class="mt4 h-0 flex flex-1 gap4">
        <div class="h-full w-100 max-md:min-w-80">
          <a-card title="部门管理" style="width: 100%;height: 100%;">
            <template #extra><a @click="openEditDepartmentDrawer(null)"><PlusOutlined class="mr-2 c-primary" />新增</a></template>
            <a-tree
              v-model:selected-keys="selectedTreeKeys"
              :tree-data="treeData"
              auto-expand-parent
              show-line
            >
              <template #title="{ data }">
                <div class="group w-full flex justify-between py-1">
                  <div>{{ data.title }}</div>
                  <div :class="{ '!block': selectedTreeKeys.includes(data.key) }" class="hidden group-hover:block" @click.stop>
                    <EditOutlined class="ml-16px c-primary" @click="openEditDepartmentDrawer(data)" />
                    <PlusOutlined class="ml-16px c-primary" @click="addBranch(data)" />
                    <DeleteOutlined class="ml-16px c-error" @click="onDel(data)" />
                  </div>
                </div>
              </template>
            </a-tree>
          </a-card>
        </div>
        <div class="flex-1">
          <c-pro-table
            ref="contactsTableRef"
            :api="getContactsAPi"
            :row-key="(record) => record.id"
            :columns="[{
                         dataIndex: 'name',
                         title: '名称',
                         key: 'name',
                         align: 'center',
                         width: '200px',
                       },
                       {
                         dataIndex: 'phoneNumber',
                         title: '手机号码',
                         key: 'phoneNumber',
                         align: 'center',
                         width: '200px',
                       }, {
                         dataIndex: 'email',
                         title: '邮箱',
                         key: 'email',
                         align: 'center',
                         width: '200px',
                       }]"
          />
        </div>
      </div>
    </div>

    <a-drawer
      v-model:open="openDepartmentEdit"
      title="添加/编辑部门"
      width="560px"
      :mask-closable="false"
    >
      <c-pro-form
        v-model:value="departmentModel"
        :descriptions="{ column: 1, bordered: true }"
        :fields="departmentFields"
        :label-col="{ style: { width: '100px' } }"
        layout="inline"
        @finish="departmentSave"
      >
        <template #parentId>
          <a-descriptions-item label="上级单位、部门">
            <a-tree-select
              v-model:value="departmentModel.parentId"
              :tree-data="treeData"
              :show-all-levels="true"
            />
          </a-descriptions-item>
        </template>
        <template #counties>
          <a-descriptions-item label="所属区县" :span="2">
            <c-standard
              s-key="county"
              :field-names="{ label: 'label', value: 'value', options: 'children' }"
            >
              <template #el="{ loading, options }">
                <c-select
                  v-if="options.find(v => v.label === departmentModel.address)?.children?.length"
                  v-model:value="departmentModel.counties!"
                  :loading="loading"
                  show-search
                  :options="options.find(v => v.label === departmentModel.address)?.children ?? []"
                />
              </template>
            </c-standard>
          </a-descriptions-item>
        </template>

        <template #footer>
          <a-descriptions-item label="操作">
            <a-button type="primary" html-type="submit">
              保存
            </a-button>
          </a-descriptions-item>
        </template>
      </c-pro-form>
    </a-drawer>
  </div>
</template>

<script lang='ts' setup>
import type { DepartmentViewModel } from '@/api/models'
import type { TreeProps } from 'ant-design-vue'
import type { DataNode } from 'ant-design-vue/es/tree'
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import { DepartmentEditModel } from '@/api/models'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { DeleteOutlined, EditOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import modal from 'ant-design-vue/es/modal'

definePage({
  meta: {
    title: '联络通讯录',
    icon: 'PhoneOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '联络通讯录',
        local: true,
        icon: 'PhoneOutlined',
        order: 4,
      },
    },
  },
})

const contactsTableRef = useTemplateRef('contactsTableRef')

const { openDepartmentEdit, departmentFields, openEditDepartmentDrawer, departmentModel, addBranch, departmentSave, treeData, selectedTreeKeys, onDel } = useDepartHook()

function search() {
  contactsTableRef.value?.search()
}

async function getContactsAPi(_param: any) {
  if (selectedTreeKeys.value?.[0]) {
    const data = await api.DepartmentManage.GetDepartmentByIdAsync({
      id: selectedTreeKeys.value[0],
    })
    return data?.contacts ?? []
  }
  else {
    return []
  }
}

watch(selectedTreeKeys, () => {
  search()
})

function useDepartHook() {
  const treeData = ref<TreeProps['treeData']>([])

  const selectedTreeKeys = ref<string[]>([])

  const openDepartmentEdit = ref(false)

  const departmentModel = ref(new DepartmentEditModel())

  const departmentFields = computed<FormField<DepartmentEditModel>[]>(() => [
    {
      label: '上级单位/部门',
      prop: 'parentId',
      el: 'input',
      attrs: {},
    },
    {
      label: '单位/部门名称',
      prop: 'name',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '单位/部门名称必填!' }],
      },
      attrs: {},
    },
    {
      label: '单位类型',
      prop: 'type',
      el: 'standard',
      formItem: {
        rules: [{ required: true, message: '所属地必填!' }],
      },
      attrs: { sKey: 'dept-type', fieldNames: { label: 'label', value: 'label' } },
    },
    {
      label: '所属地',
      prop: 'address',
      el: 'standard',
      formItem: {
        rules: [{ required: true, message: '所属地必填!' }],
      },
      attrs: { sKey: 'county', fieldNames: { label: 'label', value: 'label' }, onChange: () => {
        departmentModel.value.counties = null
      } },
    },

    {
      label: '所属区县',
      prop: 'counties',
      el: 'input',
      attrs: {},
    },
    {
      label: '是否合作单位',
      prop: 'isCooperate',
      el: 'boolean-select',
      formItem: {
        rules: [{ required: true, message: '是否合作单位必填!', trigger: ['change'] }],
      },
      emptyValue: null,
      attrs: {},
      isShow: $auth(_Role.舆情监测人员),
    },
    {
      label: '合同信息',
      prop: 'contract',
      el: 'textarea',
      formItem: {
        rules: [{ required: true, message: '合同信息必填!', trigger: ['change'] }],
      },
      attrs: {},
      isShow: $auth(_Role.舆情监测人员) && departmentModel.value.isCooperate,
    },
    {
      label: '合同开始时间',
      prop: 'contractStartTime',
      el: 'date-picker',
      formItem: {
        rules: [{ required: true, message: '合同开始时间必填!', trigger: ['change'] }],
      },
      attrs: {
        style: {
          width: '100%',
        },
      },
      isShow: $auth(_Role.舆情监测人员) && departmentModel.value.isCooperate,
    },
    {
      label: '合同结束时间',
      prop: 'contractEndTime',
      el: 'date-picker',
      formItem: {
        rules: [{ required: true, message: '合同结束时间必填!', trigger: ['change'] }],
      },
      attrs: {
        style: {
          width: '100%',
        },
      },
      isShow: $auth(_Role.舆情监测人员) && departmentModel.value.isCooperate,
    },

    {
      label: '联络人',
      prop: 'contact',
      el: 'input',
      attrs: {},
      isShow: $auth(_Role.舆情监测人员) && departmentModel.value.isCooperate,
    },
    {
      label: '联络人号码',
      prop: 'contactPhone',
      el: 'input',
      attrs: {},
      type: 'phone',
      isShow: $auth(_Role.舆情监测人员) && departmentModel.value.isCooperate,
    },
    {
      label: '备注',
      prop: 'remark',
      el: 'textarea',
      attrs: {},
      isShow: $auth(_Role.舆情监测人员),
    },
  ])

  async function openEditDepartmentDrawer(param: any) {
    try {
      departmentModel.value = param ? await api.DepartmentManage.GetDepartmentSimpleByIdAsync({ id: param.key }) : new DepartmentEditModel()
    }
    catch (error: any) {
      message.error(`查询失败:${error.message}`)
    }
    openDepartmentEdit.value = true
  }

  function addBranch(param: any) {
    departmentModel.value = {
      ...new DepartmentEditModel(),
      parentId: param?.key ?? Guid.empty,
    }
    openDepartmentEdit.value = true
  }

  async function departmentSave() {
    if (departmentModel.value.contractStartTime)
      departmentModel.value.contractStartTime = dayjs(departmentModel.value.contractStartTime).startOf('day').format()
    if (departmentModel.value.contractEndTime)
      departmentModel.value.contractStartTime = dayjs(departmentModel.value.contractStartTime).endOf('day').format()
    try {
      const data = await (Guid.isNotNull(departmentModel.value.id)
        ? api.DepartmentManage.UpdateDepartment_PostAsync(departmentModel.value)
        : api.DepartmentManage.CreateDepartment_PostAsync(departmentModel.value))
      if (data.id) {
        departmentModel.value.id = data.id
        selectedTreeKeys.value = [data.id]
      }
      getData()
      message.success('保存成功')
      openDepartmentEdit.value = false
    }
    catch (error: any) {
      message.error(`保存失败:${error.message}`)
    }
  }

  function onDel(param: any) {
    modal.confirm({
      title: `确定删除【${param.title}】部门吗`,
      icon: h(ExclamationCircleOutlined),
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          await api.DepartmentManage.DeleteDepartment_PostAsync({ departmentId: param.key })
          message.success('删除成功')
          getData()
        }
        catch (error: any) {
          message.error(`删除失败${error.message}`)
        }
      },
    })
  }

  function transformDepartmentsToTreeData(data: DepartmentViewModel[]): TreeProps['treeData'] {
    return data.map((item: DepartmentViewModel) => {
      const node: DataNode = {
        title: item.name || '',
        key: item.id,
        value: item.id,
        children: [],
      }
      node!.children = item.children ? transformDepartmentsToTreeData(item.children) : undefined
      return node
    })
  }

  async function getData() {
    try {
      const data = await api.DepartmentManage.GetAllDepartmentsAsync()
      treeData.value = transformDepartmentsToTreeData(data)
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  onMounted(() => {
    getData()
  })

  return {
    openDepartmentEdit,
    departmentFields,
    departmentModel,
    departmentSave,
    addBranch,
    openEditDepartmentDrawer,
    treeData,
    onDel,
    selectedTreeKeys,
  }
}
</script>

 <style scoped lang="less">
:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}
:deep(.ant-tree-treenode) {
  width: 100%;
  box-sizing: border-box;
  align-items: center;
  .ant-tree-node-content-wrapper {
    flex: 1;
  }
}
</style>
