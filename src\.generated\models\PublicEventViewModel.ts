import { UploadFileInfo } from "./UploadFileInfo";
import { VerificationStatus } from "./VerificationStatus";
import { EventTeacherStudent } from "./EventTeacherStudent";
import { Department } from "./Department";
import { AuditType } from "./AuditType";
import { UserBaseViewModel } from "./UserBaseViewModel";
import { PublicOpinionViewModel } from "./PublicOpinionViewModel";
import { EventReportViewModel } from "./EventReportViewModel";
import { EventSpreadSituationViewModel } from "./EventSpreadSituationViewModel";
import { CarbonCopy } from "./CarbonCopy";
export class PublicEventViewModel {
  /**Id*/
  id?: GUID = null;
  /**教育厅系统事件id，从教育厅系统复制回来*/
  jytEventId?: GUID = null;
  /**事件编号*/
  code?: string | null | undefined = null;
  /**事件名称*/
  name?: string | null | undefined = null;
  /**信息标题*/
  title?: string | null | undefined = null;
  /**信息正文*/
  content?: string | null | undefined = null;
  /**事发时间*/
  time?: Dayjs | null | undefined = null;
  /**事件等级，标准项 event_level*/
  level?: string | null | undefined = null;
  /**一级分类,标准项 event_department_type*/
  category?: string | null | undefined = null;
  /**二级分类,标准项 event_department_type*/
  subCategory?: string | null | undefined = null;
  /**联系人*/
  contacts?: string | null | undefined = null;
  /**联系人电话*/
  contactsPhone?: string | null | undefined = null;
  /**审签人*/
  reviewer?: string | null | undefined = null;
  /**附件*/
  attachment?: Array<GUID> = [];
  /**附件*/
  attachmentFile?: UploadFileInfo[] | null | undefined = [];
  /**是否紧急*/
  isItUrgent?: boolean | null | undefined = null;
  /**是否已向省级党委政府报告*/
  isItReported?: boolean | null | undefined = null;
  /**报告时间*/
  reportedTime?: Dayjs | null | undefined = null;
  /**是否涉及师生*/
  isTeachersAndStudent?: VerificationStatus | null | undefined = null;
  /**师生数据*/
  teachersAndStudentData?: EventTeacherStudent[] | null | undefined = [];
  /**是否涉及学校(机构)*/
  isItInvolveSchools?: VerificationStatus | null | undefined = null;
  /**学校(机构)*/
  schoolId?: GUID = null;
  /**学校（机构）*/
  school?: Department | null | undefined = null;
  /**所在省份*/
  province?: string | null | undefined = null;
  /**所在城市*/
  city?: string | null | undefined = null;
  /**所属区县*/
  counties?: string | null | undefined = null;
  /**事件发生地点*/
  address1?: string | null | undefined = null;
  /**是否引发舆情*/
  isPublicOpinion?: VerificationStatus | null | undefined = null;
  /**舆情访问链接*/
  publicOpinionLink?: string | null | undefined = null;
  /**舆情态势描述（当前）*/
  publicOpinionSituation?: string | null | undefined = null;
  /**是否涉及伤亡*/
  isDeath?: VerificationStatus | null | undefined = null;
  /**是否聚集*/
  isGatherACrowd?: VerificationStatus | null | undefined = null;
  /**涉及学段，标准项 event_period*/
  period?: string | null | undefined = null;
  /**跟踪要求*/
  trackContent?: string | null | undefined = null;
  /**上报状态，*/
  auditStatus: AuditType = 0;
  /**是否可以上报，在查询详情的时候赋值*/
  isCanReport: boolean = false;
  /**添加人*/
  createdBy?: GUID = null;
  /**修改人*/
  createdUser?: UserBaseViewModel | null | undefined = null;
  /**添加时间*/
  createdAt: Dayjs = dayjs();
  /**修改人*/
  modifiedBy?: GUID = null;
  /**修改人*/
  modifiedUser?: UserBaseViewModel | null | undefined = null;
  /**修改时间*/
  modifiedAt?: Dayjs | null | undefined = null;
  /**版本号*/
  version: number = 0;
  /**涉及舆情*/
  publicOpinions?: PublicOpinionViewModel[] | null | undefined = [];
  /**跟踪报告*/
  reports?: EventReportViewModel[] | null | undefined = [];
  /**传播态势*/
  spreadSituation?: EventSpreadSituationViewModel[] | null | undefined = [];
  /**关联单位*/
  carbonCopy?: CarbonCopy[] | null | undefined = [];
}
